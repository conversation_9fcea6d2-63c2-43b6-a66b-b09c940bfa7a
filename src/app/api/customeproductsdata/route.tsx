import { NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function GET(){
    const customeData = await prisma.products.findMany({
        select:{
            items:true,
            productcolor:{select:{title:true}},
            productsize :{select:{title:true}}
        }
    });

    return NextResponse.json(customeData)
}