import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

type SizeObj = {
    title: string | null,
    parenttable: string | null
}

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const parenttable = formData.get("parenttable");
    const storeData = [...formData.entries()];

    // formate the storeData to actual object
    const reformat = storeData.reduce((acc,[key,value])=>{
        const match = key.match(/^([^\[]+)\[([^\]]+)\]$/);

        if(match){
            const [_,title,indexStr] = match;
            const index = parseInt(indexStr,10);

            if(!acc[index]){
                acc[index] = {} as SizeObj;
            }

            (acc[index] as any)[title] = value;
            acc[index]["parenttable"] = typeof parenttable === "string" ? parenttable : null;
        }

        return acc;
    },[] as <PERSON>zeObj[]);

    try{
        await prisma.productsize.createMany({
            data: reformat
        });

        return NextResponse.json({message:"dress size added"},{status:200})
    }catch(error){
        return NextResponse.json(error)
    }
}