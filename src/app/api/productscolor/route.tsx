import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

type ColorObj = {
    title: string | null,
    parenttable: string | null
}

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const parenttable = formData.get("parenttable");
    const storeData = [...formData.entries()];

    // formate the storData to actual object
    const reformat = storeData.reduce((acc,[key,value])=>{
        const match = key.match(/^([^\[]+)\[([^\]]+)\]$/);

        if(match){
            const [_,title,indexStr] = match;
            const index = parseInt(indexStr,10);

            if(!acc[index]){
                acc[index] = {} as ColorObj;
            }

            (acc[index] as any)[title] = value;
            acc[index]["parenttable"] = typeof parenttable === "string" ? parenttable : null;
        }

        return acc;
    },[] as ColorObj[]);

    try{
        await prisma.productcolor.createMany({
            data: reformat
        });

        return NextResponse.json({message:"color added"},{status:200})
    }catch(error){
        return NextResponse.json(error);
    }
}